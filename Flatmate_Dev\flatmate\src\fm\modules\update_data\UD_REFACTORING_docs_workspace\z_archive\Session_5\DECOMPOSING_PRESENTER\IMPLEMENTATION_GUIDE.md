# 🚀 Implementation Guide: UpdateDataPresenter Decomposition

This guide provides a step-by-step plan for refactoring the monolithic `UpdateDataPresenter` into a set of focused, maintainable manager classes, as recommended in the architecture report.

## 1. Create New Directory Structure

First, we will create the new package structure within `update_data`. The existing `ud_presenter.py` will be moved and renamed inside this new structure.

**Action:**
1.  Create a new directory: `src/fm/modules/update_data/presenter/`
2.  Move `src/fm/modules/update_data/ud_presenter.py` to `src/fm/modules/update_data/presenter/update_data_presenter.py`
3.  Create the following empty files:

```
update_data/
└── presenter/
    ├── __init__.py
    ├── update_data_presenter.py  # The moved and renamed file
    ├── state_manager.py
    ├── source_manager.py
    ├── archive_manager.py
    ├── processing_manager.py
    └── widget_state_manager.py
```

## 2. Method Mapping

The following table maps the methods from the original `UpdateDataPresenter` to their new locations.

| Responsibility         | New Manager Class          | Original Methods                                                                                                                                                                                                |
| ---------------------- | -------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **State Management**   | `state_manager.py`         | `UpdateDataState` class, `update_can_process`, `reset`                                                                                                                                                          |
| **Source Logic**       | `source_manager.py`        | `_handle_source_select`, `_handle_folder_monitor_file_discovered`, `toggle_folder_monitoring`, `_enrich_file_info`, `_on_file_discovered`, `_handle_monitor_folder_change`, `_handle_toggle_folder_monitoring`, `_handle_source_option_change` |
| **Destination Logic**  | `archive_manager.py`       | `_handle_save_select`, `_handle_save_option_change`                                                                                                                                                             |
| **Processing Logic**   | `processing_manager.py`    | `_handle_process`, `_on_processing_started`, `_on_processing_stats`, `_on_unrecognized_files`, `_on_processing_completed`                                                                                       |
| **UI/Widget Logic**    | `widget_state_manager.py`  | `_sync_state_to_view`, `_update_guide_pane`, `_update_guide_pane_for_folder`, `_update_guide_pane_for_files`, `_handle_guide_pane_message`, `_handle_update_database_change`, `_setup_view_from_config`      |
| **Coordination**       | `update_data_presenter.py` | `__init__`, `_create_view`, `_connect_signals`, `_refresh_content` (will be refactored to delegate to managers)                                                                                                |

## 3. Refactoring Strategy (Incremental)

We will perform the refactoring incrementally to minimize risk and ensure the application remains functional at each stage.

### Step 3.1: State Manager Extraction

1.  Move the `UpdateDataState` dataclass into `state_manager.py`.
2.  In `update_data_presenter.py`, import `UpdateDataState` from the new module and instantiate it in the `__init__` method.

### Step 3.2: Manager Implementation (One by One)

For each manager (`SourceManager`, `ArchiveManager`, `ProcessingManager`, `WidgetStateManager`):

1.  **Create the class** in its respective file.
2.  **Define the `__init__` method** to accept dependencies (like the state object, view interface, or other services) via dependency injection.
3.  **Move the mapped methods** from `update_data_presenter.py` into the new manager class.
4.  **Refactor the moved methods** to operate on the injected dependencies.
5.  In `update_data_presenter.py`:
    *   Instantiate the new manager in the `__init__` method.
    *   Replace the original method calls with calls to the new manager instance (e.g., `self._handle_source_select()` becomes `self.source_manager.handle_source_select()`).

### Step 3.3: Refactor the Main Presenter

Once all logic is extracted, the `UpdateDataPresenter` will be a lean coordinator. Its primary responsibilities will be:

1.  Initializing the state and all manager classes.
2.  Connecting view signals to the appropriate manager methods.
3.  Orchestrating the overall workflow by calling manager methods.

## 4. Code Stubs

Here are basic stubs to guide the implementation.

**`state_manager.py`**
```python
from dataclasses import dataclass, field
from typing import List

@dataclass
class UpdateDataState:
    # ... (all state fields from the original class)

    def update_can_process(self) -> None:
        # ... (logic from original class)

    def reset(self) -> None:
        # ... (logic from original class)
```

**`widget_state_manager.py`**
```python
class WidgetStateManager:
    def __init__(self, view, state):
        self.view = view
        self.state = state

    def sync_state_to_view(self):
        # ... (logic from _sync_state_to_view)

    def update_guide_pane(self):
        # ... (logic from _update_guide_pane)
```

**`update_data_presenter.py` (Refactored)**
```python
from .state_manager import UpdateDataState
from .source_manager import SourceManager
from .archive_manager import ArchiveManager
from .processing_manager import ProcessingManager
from .widget_state_manager import WidgetStateManager

class UpdateDataPresenter(BasePresenter):
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        self.state = UpdateDataState()
        self.view = self._create_view()

        # Instantiate managers with dependencies
        self.widget_state_manager = WidgetStateManager(self.view, self.state)
        self.source_manager = SourceManager(self.view, self.state, self.widget_state_manager)
        self.archive_manager = ArchiveManager(self.view, self.state, self.widget_state_manager)
        self.processing_manager = ProcessingManager(self.state, self.widget_state_manager)

        self._connect_signals()
        self.widget_state_manager.sync_state_to_view()

    def _connect_signals(self):
        # Connect signals to manager methods
        self.view.source_select_requested.connect(self.source_manager.handle_source_select)
        self.view.save_select_requested.connect(self.archive_manager.handle_save_select)
        self.view.process_files_requested.connect(self.processing_manager.process_files)
        # ... etc.
```
