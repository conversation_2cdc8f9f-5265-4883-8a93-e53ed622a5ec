"""
Individual widget definitions for Update Data left panel.

Just widget definitions - no layout, no UI coordination.
Layout is handled by LeftPanelManager.
"""

from fm.gui._shared_components.widgets import (
    LabeledCheckBox,
    ActionButton,
    ExitButton,
    SelectOptionGroupVLayout
)


class SourceOptionsGroup(SelectOptionGroupVLayout):
    """Source file selection widget (folder vs individual files)."""

    def __init__(self, parent=None):
        super().__init__(
            options=["Select entire folder...", "Select individual files..."],
            label_text="Source Files",
            button_text="Select",
            parent=parent
        )


class ArchiveOptionsGroup(SelectOptionGroupVLayout):
    """Archive/save location selection widget."""

    def __init__(self, parent=None):
        super().__init__(
            options=["Same as Source Files", "Select save location..."],
            label_text="Archive",
            button_text="Select",
            parent=parent
        )
        # Initially disabled until source is selected
        self.set_button_enabled(False)


class DatabaseCheckbox(LabeledCheckBox):
    """Database update option checkbox."""

    def __init__(self, parent=None):
        super().__init__(
            label_text="Update Database",
            checked=True,  # Default to checked
            tooltip="Store processed transactions in the central database",
            parent=parent
        )


class ProcessActionButton(ActionButton):
    """Primary action button - context dependent text."""

    def __init__(self, parent=None):
        super().__init__("Process Files", parent)
        self.setEnabled(False)  # Initially disabled


class CancelExitButton(ExitButton):
    """Exit/cancel button - context dependent text."""

    def __init__(self, parent=None):
        super().__init__("Cancel", parent)


