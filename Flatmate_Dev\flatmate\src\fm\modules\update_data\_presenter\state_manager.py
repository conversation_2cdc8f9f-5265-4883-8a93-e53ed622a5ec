"""
State management for Update Data module.

This module contains the UpdateDataState dataclass and consolidated state management logic.
Consolidates StateManager and WidgetStateManager as part of the consolidation refactoring.
"""

from dataclasses import dataclass, field
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from ..interface import IUpdateDataView


@dataclass
class UpdateDataState:
    """
    Presenter state for Update Data module.

    Following MVP pattern: Presenter owns all state, View is stateless.
    This replaces the archived view_context_manager approach.
    """
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"

    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""

    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0

    def update_can_process(self) -> None:
        """Update can_process based on current state."""
        self.can_process = (
            self.source_configured and
            self.destination_configured and
            not self.processing and
            len(self.selected_files) > 0
        )

        # Update process button text - keep it simple
        if self.processing:
            self.process_button_text = "Processing..."
        else:
            self.process_button_text = "Process Files"

    def reset(self) -> None:
        """Reset state to initial values."""
        self.source_configured = False
        self.source_type = ""
        self.source_path = ""
        self.selected_files.clear()
        self.destination_configured = False
        self.processing = False
        self.error_message = ""
        self.status_message = "Select source files or folder to begin"
        self.update_can_process()


class StateManager:
    """
    Consolidated state and UI synchronization management.

    This class combines:
    - State data management (UpdateDataState)
    - UI synchronization (from WidgetStateManager)
    - State-to-view updates
    - Guide pane management
    """

    def __init__(self, view: 'IUpdateDataView', info_bar_service, folder_monitor_service):
        """
        Initialize the consolidated state manager.

        Args:
            view: The view interface for UI updates
            info_bar_service: Service for info bar messages
            folder_monitor_service: Service for folder monitoring
        """
        # State data
        self.state = UpdateDataState()

        # UI sync dependencies
        self.view = view
        self.info_bar_service = info_bar_service
        self.folder_monitor_service = folder_monitor_service

    # =============================================================================
    # STATE MANAGEMENT METHODS
    # =============================================================================

    def update_can_process(self):
        """Delegate to state object."""
        self.state.update_can_process()

    def reset(self):
        """Delegate to state object."""
        self.state.reset()

    # =============================================================================
    # UI SYNCHRONIZATION METHODS (from WidgetStateManager)
    # =============================================================================

    def sync_state_to_view(self) -> None:
        """
        Sync presenter state to view.

        MVP pattern: Presenter state is source of truth, view reflects state.
        This replaces the archived view_context_manager approach.
        """
        try:
            # Update process button state
            self.view.set_process_button_enabled(self.state.can_process)
            self.view.set_process_button_text(self.state.process_button_text)

            # Update status message
            if self.state.status_message:
                self.info_bar_service.show_info(self.state.status_message)

            # Update guide pane based on current state
            self.update_guide_pane()

        except Exception as e:
            # Log error but don't crash the application
            self.info_bar_service.show_error(f"Error syncing state to view: {e}")

    def update_guide_pane(self) -> None:
        """Update guide pane content based on current state."""
        try:
            if self.state.source_type == 'folder':
                self.update_guide_pane_for_folder()
            elif self.state.source_type == 'files':
                self.update_guide_pane_for_files()
            else:
                # Default guide content
                self.view.set_guide_content("Select files or folder to begin processing.")

        except Exception as e:
            self.info_bar_service.show_error(f"Error updating guide pane: {e}")

    def update_guide_pane_for_folder(self) -> None:
        """Update guide pane for folder source type."""
        try:
            if hasattr(self.view, 'guide_pane') and self.view.guide_pane:
                folder_path = getattr(self.state, 'selected_folder', self.state.source_path)

                # Show folder monitoring status
                is_monitoring = self.folder_monitor_service.is_monitoring()
                monitor_status = "ON" if is_monitoring else "OFF"

                guide_text = f"""
Folder Source: {folder_path}
Folder Monitoring: {monitor_status}

Files will be processed from this folder.
Toggle monitoring to automatically detect new files.
                """.strip()

                self.view.set_guide_content(guide_text)

        except Exception as e:
            self.info_bar_service.show_error(f"Error updating folder guide: {e}")

    def update_guide_pane_for_files(self) -> None:
        """Update guide pane for files source type."""
        try:
            if hasattr(self.view, 'guide_pane') and self.view.guide_pane:
                file_count = len(self.state.selected_files)

                guide_text = f"""
Selected Files: {file_count}
Source Type: Individual Files

Files are ready for processing.
Check the file list to review selected files.
                """.strip()

                self.view.set_guide_content(guide_text)

        except Exception as e:
            self.info_bar_service.show_error(f"Error updating files guide: {e}")
