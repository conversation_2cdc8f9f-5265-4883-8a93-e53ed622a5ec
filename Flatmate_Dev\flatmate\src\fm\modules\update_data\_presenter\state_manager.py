"""
State management for Update Data module.

This module contains the UpdateDataState dataclass and related state management logic.
Extracted from the monolithic UpdateDataPresenter as part of the decomposition refactoring.
"""

from dataclasses import dataclass, field
from typing import List


@dataclass
class UpdateDataState:
    """
    Presenter state for Update Data module.

    Following MVP pattern: Presenter owns all state, View is stateless.
    This replaces the archived view_context_manager approach.
    """
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"

    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""

    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0

    def update_can_process(self) -> None:
        """Update can_process based on current state."""
        self.can_process = (
            self.source_configured and
            self.destination_configured and
            not self.processing and
            len(self.selected_files) > 0
        )

        # Update process button text - keep it simple
        if self.processing:
            self.process_button_text = "Processing..."
        else:
            self.process_button_text = "Process Files"

    def reset(self) -> None:
        """Reset state to initial values."""
        self.source_configured = False
        self.source_type = ""
        self.source_path = ""
        self.selected_files.clear()
        self.destination_configured = False
        self.processing = False
        self.error_message = ""
        self.status_message = "Select source files or folder to begin"
        self.update_can_process()
