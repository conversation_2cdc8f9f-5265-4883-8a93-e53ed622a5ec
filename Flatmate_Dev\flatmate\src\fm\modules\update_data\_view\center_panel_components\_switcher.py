"""
Switcher for pane components in the center panel.
"""

from typing import Dict, Optional

from PySide6.QtWidgets import QStackedWidget, QVBoxLayout, QWidget

# Import the proper base component from GUI shared components
from fm.gui._shared_components.base import BasePanelComponent


class PanelSwitcher(QWidget):
    """A switcher that can hold and switch between multiple pane components.
    
    This implements the Composite part of the Composite pattern,
    allowing multiple panes to be managed as a single unit with easy switching.
    """
    
    def __init__(self, parent=None):
        """Initialize the panel switcher."""
        super().__init__(parent)
        
        # Set up layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Use stacked widget to show only one component at a time
        self.stack = QStackedWidget()
        self.main_layout.addWidget(self.stack)
        
        # Track components by name
        self.components: Dict[str, BasePanelComponent] = {}
        self.active_component: Optional[str] = None
    
    def add_component(self, name: str, component: BasePanelComponent):
        """Add a component to this composite.
        
        Args:
            name: Unique identifier for the component
            component: The component to add
        """
        if name in self.components:
            # Replace existing component
            old_component = self.components[name]
            index = self.stack.indexOf(old_component)
            if index >= 0:
                self.stack.removeWidget(old_component)
        
        # Add the new component
        self.components[name] = component
        self.stack.addWidget(component)
    
    def remove_component(self, name: str):
        """Remove a component from this composite.
        
        Args:
            name: Identifier of the component to remove
        """
        if name in self.components:
            component = self.components[name]
            self.stack.removeWidget(component)
            del self.components[name]
            
            # Update active component if needed
            if self.active_component == name:
                self.active_component = None
    
    def show_component(self, name: str = ""):
        """Show a specific component or the entire composite.
        
        Args:
            name: Name of component to show, or empty string to show the active component
        """
        if not name:
            # Show the entire composite
            super().show()
            return
            
        if name in self.components:
            component = self.components[name]
            self.stack.setCurrentWidget(component)
            self.active_component = name
            super().show()
    
    def hide_component(self):
        """Hide the entire composite."""
        super().hide()
    
    def get_component(self, name: str) -> Optional[BasePanelComponent]:
        """Get a component by name.
        
        Args:
            name: Name of the component to retrieve
            
        Returns:
            The component if found, None otherwise
        """
        return self.components.get(name)
    
    def get_current_component(self) -> Optional[BasePanelComponent]:
        """Get the currently active component.
        
        Returns:
            The currently active component, or None if no component is active
        """
        if self.active_component and self.active_component in self.components:
            return self.components[self.active_component]
        return None
        
    def get_current_component_id(self) -> Optional[str]:
        """Get the ID of the currently active component.
        
        Returns:
            The ID of the currently active component, or None if no component is active
        """
        return self.active_component
