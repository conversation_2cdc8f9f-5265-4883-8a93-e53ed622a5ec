"""
File management for Update Data module.

This module consolidates source and archive management logic including:
- File and folder selection
- Save location selection and "same as source" logic
- Folder monitoring
- File discovery and enrichment
- Source and save option changes

Consolidated from SourceManager and ArchiveManager as part of the consolidation refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING

from ....core.services.logger import log
from ..config.option_types import SourceOptions, SaveOptions
from ..services.file_info_service import FileInfoService
from ..services.events_data import SourceDiscoveredEvent, FileDisplayUpdateEvent
from ..services.local_event_bus import ViewEvents

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_manager import UpdateDataState, StateManager


class FileManager:
    """
    Manages all file/folder selection and save location logic.

    This class consolidates:
    - File and folder selection dialogs
    - File enrichment using FileInfoService
    - Folder monitoring integration
    - Save location selection and "same as source" functionality
    - Source and save option changes
    """

    def __init__(self, view: 'IUpdateDataView', state_manager: 'StateManager',
                 folder_monitor_service, local_bus, info_bar_service):
        """
        Initialize the file manager.

        Args:
            view: The view interface
            state_manager: Consolidated state and UI sync manager
            folder_monitor_service: Service for folder monitoring
            local_bus: Local event bus for module events
            info_bar_service: Service for info bar messages
        """
        self.view = view
        self.state_manager = state_manager  # Consolidated state + widget state manager
        self.state = state_manager.state  # Direct access to state data
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # File info service for enriching file data
        self.file_info_service = FileInfoService()
        
        # Track selected source for "same as source" functionality
        self.selected_source = None

    # =============================================================================
    # SOURCE SELECTION METHODS (from SourceManager)
    # =============================================================================

    def handle_source_select(self, selection_type):
        """
        Handle source selection request from the view.
        
        Args:
            selection_type: Type of selection ('files' or 'folder')
        """
        log.debug(f"Source selection requested: {selection_type}")
        
        if selection_type == 'files':
            self._select_files()
        elif selection_type == 'folder':
            self._select_folder()
        else:
            log.warning(f"Unknown selection type: {selection_type}")
            
        # After source selection, update save location if "same as source"
        self._update_save_location_for_source()
        
        # Update UI state
        self.state_manager.sync_state_to_view()

    def _select_files(self):
        """Select individual files using file dialog."""
        try:
            file_paths = self.view.show_file_dialog()
            if file_paths:
                log.debug(f"Files selected: {file_paths}")
                self.selected_source = file_paths
                enriched_files = self.enrich_file_info(file_paths)
                self.state.selected_files = enriched_files
                self.state.source_type = 'files'
                self.state.update_can_process()
                
                # Emit file display update event
                source_path = file_paths[0] if file_paths else ""
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED,
                                  FileDisplayUpdateEvent(files=enriched_files, source_path=source_path))
                
        except Exception as e:
            log.error(f"Error selecting files: {e}")
            self.info_bar_service.show_error(f"Error selecting files: {e}")

    def _select_folder(self):
        """Select a folder using folder dialog."""
        try:
            folder_path = self.view.show_folder_dialog()
            if folder_path:
                log.debug(f"Folder selected: {folder_path}")
                self.selected_source = folder_path
                self.state.selected_folder = folder_path
                self.state.source_type = 'folder'
                self.state.update_can_process()
                
                # Emit source discovered event for folder monitoring
                self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED,
                                  SourceDiscoveredEvent(
                                      source_type='folder',
                                      files=[],
                                      path=folder_path,
                                      count=0
                                  ))
                
        except Exception as e:
            log.error(f"Error selecting folder: {e}")
            self.info_bar_service.show_error(f"Error selecting folder: {e}")

    def enrich_file_info(self, file_paths):
        """
        Enrich file paths with additional metadata.

        Args:
            file_paths: List of file paths to enrich

        Returns:
            List of enriched file info dictionaries
        """
        try:
            return self.file_info_service.discover_files(file_paths)
        except Exception as e:
            log.error(f"Error enriching file info: {e}")
            return [{'path': path, 'name': os.path.basename(path)} for path in file_paths]

    def handle_folder_monitor_file_discovered(self, event_data):
        """
        Handle file discovered event from folder monitoring.
        
        Args:
            event_data: Event data containing discovered files
        """
        try:
            if hasattr(event_data, 'files') and event_data.files:
                log.debug(f"Files discovered via folder monitoring: {len(event_data.files)}")
                enriched_files = self.enrich_file_info(event_data.files)
                self.state.selected_files = enriched_files
                self.state.update_can_process()
                
                # Update file display
                source_path = event_data.files[0] if event_data.files else ""
                self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED,
                                  FileDisplayUpdateEvent(files=enriched_files, source_path=source_path))
                
                # Update UI state
                self.state_manager.sync_state_to_view()
                
        except Exception as e:
            log.error(f"Error handling discovered files: {e}")

    def toggle_folder_monitoring(self):
        """Toggle folder monitoring on/off."""
        try:
            current_state = self.folder_monitor_service.is_monitoring()
            if current_state:
                self.folder_monitor_service.stop_monitoring()
                log.debug("Folder monitoring stopped")
            else:
                if self.state.selected_folder:
                    self.folder_monitor_service.start_monitoring(self.state.selected_folder)
                    log.debug(f"Folder monitoring started for: {self.state.selected_folder}")
                else:
                    self.info_bar_service.show_warning("No folder selected for monitoring")
                    
        except Exception as e:
            log.error(f"Error toggling folder monitoring: {e}")
            self.info_bar_service.show_error(f"Error with folder monitoring: {e}")

    def handle_monitor_folder_change(self, enabled):
        """
        Handle folder monitoring enable/disable change.
        
        Args:
            enabled: Boolean indicating if monitoring should be enabled
        """
        try:
            if enabled and self.state.selected_folder:
                self.folder_monitor_service.start_monitoring(self.state.selected_folder)
                log.debug(f"Folder monitoring enabled for: {self.state.selected_folder}")
            else:
                self.folder_monitor_service.stop_monitoring()
                log.debug("Folder monitoring disabled")
                
        except Exception as e:
            log.error(f"Error changing folder monitoring state: {e}")

    def handle_source_option_change(self, option):
        """
        Handle source option changes.

        Args:
            option: The new source option value
        """
        try:
            log.debug(f"Source option changed to: {option}")
            self.state.source_option = option

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling source option change: {e}")

    # =============================================================================
    # SAVE LOCATION METHODS (from ArchiveManager)
    # =============================================================================

    def handle_save_select(self):
        """Handle save location selection request from the view."""
        try:
            save_path = self.view.show_folder_dialog()
            if save_path:
                log.debug(f"Save location selected: {save_path}")
                self.state.save_location = save_path
                self.state.update_can_process()
                
                # Update UI state
                self.state_manager.sync_state_to_view()
                
        except Exception as e:
            log.error(f"Error selecting save location: {e}")
            self.info_bar_service.show_error(f"Error selecting save location: {e}")

    def handle_save_option_change(self, option):
        """
        Handle save option changes.

        Args:
            option: The new save option value
        """
        try:
            log.debug(f"Save option changed to: {option}")
            self.state.save_option = option

            # Update save location based on option
            self._update_save_location_for_source()

            # Update UI state
            self.state_manager.sync_state_to_view()

        except Exception as e:
            log.error(f"Error handling save option change: {e}")

    # =============================================================================
    # INTERNAL CONSOLIDATION METHODS
    # =============================================================================

    def _update_save_location_for_source(self):
        """
        Handle 'same as source' logic internally.
        
        This method consolidates the cross-manager communication that was
        previously handled by method wrapping in the presenter.
        """
        try:
            if (self.state.save_option == SaveOptions.SAME_AS_SOURCE and 
                self.selected_source is not None):
                
                if self.state.source_type == 'folder':
                    # For folders, use the folder path directly
                    self.state.save_location = self.selected_source
                    log.debug(f"Save location set to source folder: {self.selected_source}")
                    
                elif self.state.source_type == 'files' and self.selected_source:
                    # For files, use the directory of the first file
                    first_file_path = self.selected_source[0] if isinstance(self.selected_source, list) else self.selected_source
                    save_dir = os.path.dirname(first_file_path)
                    self.state.save_location = save_dir
                    log.debug(f"Save location set to source directory: {save_dir}")
                    
                self.state.update_can_process()
                
        except Exception as e:
            log.error(f"Error updating save location for source: {e}")
