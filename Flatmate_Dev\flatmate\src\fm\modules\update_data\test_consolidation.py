#!/usr/bin/env python3
"""
Test script for FileManager consolidation.

This script tests the basic functionality of the consolidated FileManager
to ensure the refactoring didn't break core functionality.
"""

import sys
import os
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.fm.modules.update_data._presenter.file_manager import FileManager
from src.fm.modules.update_data._presenter.state_manager import UpdateDataState
from src.fm.modules.update_data.config.option_types import SaveOptions, SourceOptions


class MockView:
    """Mock view for testing."""
    def show_file_dialog(self):
        return ["/test/file1.csv", "/test/file2.csv"]
    
    def show_folder_dialog(self):
        return "/test/folder"


class MockStateManager:
    """Mock state manager for testing."""
    def sync_state_to_view(self):
        pass


class MockService:
    """Mock service for testing."""
    def show_error(self, msg):
        print(f"ERROR: {msg}")
    
    def show_warning(self, msg):
        print(f"WARNING: {msg}")
    
    def emit(self, event, data):
        print(f"EVENT: {event} - {data}")
    
    def register_callback(self, callback):
        pass
    
    def is_monitoring(self):
        return False
    
    def start_monitoring(self, path):
        print(f"Started monitoring: {path}")
    
    def stop_monitoring(self):
        print("Stopped monitoring")


def test_file_manager_creation():
    """Test that FileManager can be created successfully."""
    print("Testing FileManager creation...")
    
    # Create mock dependencies
    view = MockView()
    state = UpdateDataState()
    state_manager = MockStateManager()
    folder_monitor_service = MockService()
    local_bus = MockService()
    info_bar_service = MockService()
    
    # Create FileManager
    file_manager = FileManager(
        view=view,
        state=state,
        state_manager=state_manager,
        folder_monitor_service=folder_monitor_service,
        local_bus=local_bus,
        info_bar_service=info_bar_service
    )
    
    print("✓ FileManager created successfully")
    return file_manager, state


def test_file_selection(file_manager, state):
    """Test file selection functionality."""
    print("\nTesting file selection...")
    
    # Test file selection
    file_manager.handle_source_select('files')
    
    # Check state was updated
    assert state.selected_files is not None, "Selected files should be set"
    assert state.source_type == 'files', "Source type should be 'files'"
    
    print("✓ File selection works")


def test_folder_selection(file_manager, state):
    """Test folder selection functionality."""
    print("\nTesting folder selection...")
    
    # Test folder selection
    file_manager.handle_source_select('folder')
    
    # Check state was updated
    assert state.selected_folder is not None, "Selected folder should be set"
    assert state.source_type == 'folder', "Source type should be 'folder'"
    
    print("✓ Folder selection works")


def test_same_as_source_functionality(file_manager, state):
    """Test 'same as source' functionality."""
    print("\nTesting 'same as source' functionality...")
    
    # Set save option to same as source
    state.save_option = SaveOptions.SAME_AS_SOURCE
    
    # Test with folder source
    file_manager.handle_source_select('folder')
    assert state.save_location == state.selected_folder, "Save location should match folder source"
    
    # Test with file source
    file_manager.handle_source_select('files')
    expected_dir = os.path.dirname(file_manager.selected_source[0])
    assert state.save_location == expected_dir, "Save location should match file directory"
    
    print("✓ 'Same as source' functionality works")


def test_save_option_changes(file_manager, state):
    """Test save option changes."""
    print("\nTesting save option changes...")
    
    # Test save option change
    file_manager.handle_save_option_change(SaveOptions.SELECT_LOCATION)
    assert state.save_option == SaveOptions.SELECT_LOCATION, "Save option should be updated"
    
    print("✓ Save option changes work")


def test_source_option_changes(file_manager, state):
    """Test source option changes."""
    print("\nTesting source option changes...")
    
    # Test source option change
    file_manager.handle_source_option_change(SourceOptions.SELECT_FILES)
    assert state.source_option == SourceOptions.SELECT_FILES, "Source option should be updated"
    
    print("✓ Source option changes work")


def main():
    """Run all tests."""
    print("=== FileManager Consolidation Tests ===\n")
    
    try:
        # Test creation
        file_manager, state = test_file_manager_creation()
        
        # Test functionality
        test_file_selection(file_manager, state)
        test_folder_selection(file_manager, state)
        test_same_as_source_functionality(file_manager, state)
        test_save_option_changes(file_manager, state)
        test_source_option_changes(file_manager, state)
        
        print("\n=== All Tests Passed! ===")
        print("FileManager consolidation is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
