"""
Presenter package for Update Data module.

This package contains the decomposed presenter components:
- update_data_presenter.py: Main coordinator
- state_manager.py: State management
- source_manager.py: Source selection logic
- archive_manager.py: Archive location logic
- processing_manager.py: File processing logic
- widget_state_manager.py: UI state management
"""

# Export the main presenter for backward compatibility
# Note: Since ud_presenter.py imports from this package, we can't import it here
# The presenter is available directly via the module import

__all__ = ['UpdateDataPresenter']