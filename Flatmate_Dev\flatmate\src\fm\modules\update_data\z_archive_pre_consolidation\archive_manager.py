"""
Archive/destination management for Update Data module.

This module handles all destination/save location logic including:
- Save location selection
- Save option changes
- Archive configuration

Extracted from the monolithic UpdateDataPresenter as part of the decomposition refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING

from ....core.services.logger import log # >> # TODO: no logging 
from ..config.ud_config import ud_config
from ..config.ud_keys import UpdateDataKeys
from ..config.option_types import SaveOptions

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_manager import UpdateDataState
    from .widget_state_manager import WidgetStateManager


class ArchiveManager:
    """
    Manages all save/archive location logic.

    This class handles:
    - Save location selection dialogs
    - Save option changes (same as source vs custom)
    - Archive path management and validation
    - State coordinator integration for destinations
    """

    def __init__(self, view: 'IUpdateDataView', state: 'UpdateDataState',
                 widget_state_manager: 'WidgetStateManager', info_bar_service,
                 state_coordinator=None):
        """
        Initialize the archive manager.

        Args:
            view: The view interface
            state: The presenter state object
            widget_state_manager: Manager for widget state updates
            info_bar_service: Service for info bar messages
            state_coordinator: Optional state coordinator for legacy support
        """
        self.view = view
        self.state = state
        self.widget_state_manager = widget_state_manager
        self.info_bar_service = info_bar_service
        self.state_coordinator = state_coordinator

        # Legacy state tracking (will be migrated to state manager)
        self.save_location = None
        self.selected_source = None  # Reference to source selection

    def handle_save_select(self):
        """Handle save location selection."""
        folder = self.view.show_folder_dialog(
            "Select Save Location",
            initial_dir=str(ud_config.get_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, default=str(Path.home()))),
        )
        if folder:
            self.save_location = folder
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, folder)
            self.view.set_save_path(folder)

            # Notify SimpleStateCoordinator about custom destination
            if self.state_coordinator:
                self.state_coordinator.set_destination_custom(folder)
                

            # Update state
            self.state.destination_configured = True
            self.state.save_path = folder
            self.state.update_can_process()
            self.widget_state_manager.sync_state_to_view()

            self.info_bar_service.publish_message(f"Save location: {folder}")

    def handle_save_option_change(self, option: str):
        """Handle save location option change."""
        # Disable save select button when using same as source
        is_same_as_source = option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        if is_same_as_source:
            # Update the save location label to indicate "Same as source..."
            self.view.set_save_path("Same as source...")

            if self.selected_source:
                # If we have a selected source, use its directory
                if self.selected_source["type"] == "folder":
                    self.save_location = self.selected_source["path"]
                else:  # files
                    self.save_location = os.path.dirname(
                        self.selected_source["file_paths"][0]
                    )
                self.info_bar_service.publish_message(
                    f"Save location: {self.save_location}"
                )

                # Notify SimpleStateCoordinator about destination selection
                if self.state_coordinator:
                    self.state_coordinator.set_destination_same_as_source()

                # Update state
                self.state.destination_configured = True
                self.state.save_path = self.save_location
                self.state.update_can_process()
                self.widget_state_manager.sync_state_to_view()
        else:
            # Reset save location if not using same as source
            self.save_location = None
            self.view.set_save_path("")  # Clear the save path label
            self.info_bar_service.publish_message("")
            # TODO: # ? and where we get the infobar service from !?
            # ! WE HAVENT IMPORTED IT! gui.services.InfoBarService (if exposed in init)
            # Update state
            self.state.destination_configured = False
            self.state.save_path = ""
            self.state.update_can_process()
            self.widget_state_manager.sync_state_to_view()

    def set_selected_source(self, selected_source):
        """
        Set the selected source reference for same-as-source functionality.

        Args:
            selected_source: Dictionary containing source information
        """
        # TODO: Archive doesnt own the source state and certainly doesnt set it!?
        # if anything it should get the source folder - this is not readable code.
        self.selected_source = selected_source

        # If save option is "Same as source", update the save location now
        if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
            self.handle_save_option_change(SaveOptions.SAME_AS_SOURCE.value)
